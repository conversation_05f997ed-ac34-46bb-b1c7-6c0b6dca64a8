#!/usr/bin/env node

/**
 * 系統 API 測試腳本
 * 用於測試所有系統 API 端點的連通性
 */

const axios = require('axios');

// 配置
const BASE_URL = 'https://sbar-posweb-api.pascation.com.tw';
const TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vc2Jhci1wb3N3ZWItYXBpLnBhc2NhdGlvbi5jb20udHcvYXBpL2xvZ2luIiwiaWF0IjoxNzU4MTIzOTk3LCJleHAiOjE3NTgyMTAzOTcsIm5iZiI6MTc1ODEyMzk5NywianRpIjoiemlLTXJHeU5valF0bmhDTiIsInN1YiI6IjlmMWM1Yzc0LWM3ZmYtNDAwOC1hMDYzLWExYTFkNjI3NWU5YyIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.fR_QP4kmbXqxM3WNTKhdCWcC9u_mfzv53bxEPRaq8Os';

// API 端點列表
const API_ENDPOINTS = [
  { name: '地區資料', path: '/api/districts' },
  { name: '付款方式', path: '/api/payment-methods' },
  { name: '服務地點設施', path: '/api/service-location-facilities' },
  { name: '服務地點服務', path: '/api/service-location-services' },
  { name: '服務地點店鋪', path: '/api/service-location-stores' },
  { name: '性別資料', path: '/api/gender' },
  { name: '已知管道', path: '/api/known-pipeline' },
  { name: '課程群組', path: '/api/course-groups' },
  { name: '課程資料', path: '/api/courses' },
  { name: '城市資料', path: '/api/cities' },
  { name: '美容師服務部位', path: '/api/beautician-service-parts' },
  { name: '活動日誌', path: '/api/activity-logs' },
  { name: '活動日誌統計', path: '/api/activity-logs/statistics' }
];

// 創建 axios 實例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TOKEN}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000
});

// 測試單個 API 端點
async function testEndpoint(endpoint) {
  try {
    console.log(`\n🔍 測試 ${endpoint.name} (${endpoint.path})`);
    
    const startTime = Date.now();
    const response = await api.get(endpoint.path);
    const endTime = Date.now();
    
    const responseTime = endTime - startTime;
    const status = response.status;
    const data = response.data;
    
    // 分析響應數據
    let dataCount = 0;
    let dataType = 'unknown';
    
    if (Array.isArray(data)) {
      dataCount = data.length;
      dataType = 'array';
    } else if (data && typeof data === 'object') {
      if (data.data && Array.isArray(data.data)) {
        dataCount = data.data.length;
        dataType = 'paginated';
      } else if (data.data) {
        dataCount = 1;
        dataType = 'object';
      } else {
        dataCount = Object.keys(data).length;
        dataType = 'object';
      }
    }
    
    console.log(`✅ 成功 - 狀態: ${status}, 響應時間: ${responseTime}ms, 數據類型: ${dataType}, 記錄數: ${dataCount}`);
    
    // 顯示部分數據結構
    if (data && typeof data === 'object') {
      const sampleData = Array.isArray(data) ? data[0] : 
                        (data.data && Array.isArray(data.data)) ? data.data[0] : data;
      
      if (sampleData) {
        const fields = Object.keys(sampleData).slice(0, 5);
        console.log(`   字段示例: ${fields.join(', ')}${fields.length < Object.keys(sampleData).length ? '...' : ''}`);
      }
    }
    
    return {
      success: true,
      status,
      responseTime,
      dataCount,
      dataType,
      error: null
    };
    
  } catch (error) {
    const status = error.response?.status || 'N/A';
    const message = error.response?.data?.message || error.message;
    
    console.log(`❌ 失敗 - 狀態: ${status}, 錯誤: ${message}`);
    
    return {
      success: false,
      status,
      responseTime: 0,
      dataCount: 0,
      dataType: 'error',
      error: message
    };
  }
}

// 測試所有端點
async function testAllEndpoints() {
  console.log('🚀 開始測試系統 API 端點...\n');
  console.log(`📡 基礎 URL: ${BASE_URL}`);
  console.log(`🔑 使用 Token: ${TOKEN.substring(0, 20)}...`);
  
  const results = [];
  let successCount = 0;
  let totalResponseTime = 0;
  
  for (const endpoint of API_ENDPOINTS) {
    const result = await testEndpoint(endpoint);
    result.name = endpoint.name;
    result.path = endpoint.path;
    results.push(result);
    
    if (result.success) {
      successCount++;
      totalResponseTime += result.responseTime;
    }
    
    // 添加延遲避免請求過於頻繁
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 顯示總結
  console.log('\n' + '='.repeat(60));
  console.log('📊 測試總結');
  console.log('='.repeat(60));
  console.log(`總端點數: ${API_ENDPOINTS.length}`);
  console.log(`成功: ${successCount}`);
  console.log(`失敗: ${API_ENDPOINTS.length - successCount}`);
  console.log(`成功率: ${((successCount / API_ENDPOINTS.length) * 100).toFixed(1)}%`);
  
  if (successCount > 0) {
    console.log(`平均響應時間: ${(totalResponseTime / successCount).toFixed(0)}ms`);
  }
  
  // 顯示失敗的端點
  const failedEndpoints = results.filter(r => !r.success);
  if (failedEndpoints.length > 0) {
    console.log('\n❌ 失敗的端點:');
    failedEndpoints.forEach(endpoint => {
      console.log(`   ${endpoint.name}: ${endpoint.error}`);
    });
  }
  
  // 顯示成功的端點統計
  const successEndpoints = results.filter(r => r.success);
  if (successEndpoints.length > 0) {
    console.log('\n✅ 成功的端點統計:');
    successEndpoints.forEach(endpoint => {
      console.log(`   ${endpoint.name}: ${endpoint.dataCount} 條記錄, ${endpoint.responseTime}ms`);
    });
  }
  
  console.log('\n🎉 測試完成！');
  
  return results;
}

// 主函數
async function main() {
  try {
    await testAllEndpoints();
  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error.message);
    process.exit(1);
  }
}

// 如果直接運行此腳本
if (require.main === module) {
  main();
}

module.exports = { testAllEndpoints, testEndpoint };
