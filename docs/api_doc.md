### Activity Logs (活動日誌)

- **GET /api/activity-logs**: 取得活動日誌 (支援篩選與分頁)
- **GET /api/activity-logs/statistics**: 取得活動日誌統計數據
- **GET /api/activity-logs/filter-options**: 取得活動日誌的篩選選項

### Article Categories (文章分類)

- **GET /api/article-categories**: 取得所有文章分類
- **PUT /api/article-categories/batch-update**: 批次更新多個文章分類
- **GET /api/article-categories/status/{status}**: 依狀態取得文章分類

### Articles (文章)

- **GET /api/articles**: 取得所有文章 (支援篩選)
- **POST /api/articles**: 建立新文章
- **GET /api/articles/{id}**: 依 ID 取得單一文章
- **PUT /api/articles/{id}**: 更新文章
- **DELETE /api/articles/{id}**: 刪除文章
- **GET /api/articles/homepage**: 取得顯示於首頁的文章

### Auth (認證)

- **POST /api/login**: 使用者登入
- **POST /api/login/cms-user**: CMS 使用者登入
- **GET /api/my-profile**: 取得目前使用者的個人資料與角色
- **POST /api/login/member**: 會員登入
- **POST /api/login/member/verify**: 驗證會員登入的 OTP

### Beautician Service Parts (美容師服務部位)

- **GET /api/beautician-service-parts**: 取得美容師服務部位列表
- **POST /api/beautician-service-parts**: 建立新的美容師服務部位
- **GET /api/beautician-service-parts/{code}**: 取得美容師服務部位詳細資料
- **PUT /api/beautician-service-parts/{code}**: 更新美容師服務部位
- **DELETE /api/beautician-service-parts/{code}**: 刪除美容師服務部位
- **PATCH /api/beautician-service-parts/{code}/state**: 變更美容師服務部位狀態

### Brand Product Categories (品牌商品分類)

- **GET /api/brand/product-categories**: 取得所有品牌商品分類
- **PUT /api/brand/product-categories/batch-update**: 取代所有品牌商品分類

### Brand Product Origins (品牌商品產地)

- **GET /api/brand/product-origins**: 取得所有商品產地
- **PUT /api/brand/product-origins/batch-update**: 取代所有商品產地

### Brand Product Targets (品牌商品適用對象)

- **GET /api/brand/product-targets**: 取得所有品牌商品適用對象
- **PUT /api/brand/product-targets/batch-update**: 取代所有品牌商品適用對象

### Brand Product Values (品牌商品價值)

- **GET /api/brand/product-values**: 取得所有品牌商品價值
- **PUT /api/brand/product-values/batch-update**: 取代所有品牌商品價值

### Cities (城市)

- **GET /api/cities**: 取得城市列表

### Course Add-Ons (課程加購項目)

- **GET /api/course-add-ons**: 取得所有課程加購項目
- **PUT /api/course-add-ons/batch-update**: 批次更新課程加購項目

### Courses (課程)

- **GET /api/courses**: 取得課程列表 (支援搜尋與篩選)
- **POST /api/courses**: 建立新課程
- **GET /api/courses/{id}**: 取得指定課程
- **PUT /api/courses/{id}**: 更新指定課程
- **DELETE /api/courses/{id}**: 刪除指定課程

### Course FAQs (課程常見問題)

- **GET /api/courses/{courseId}/faqs**: 依課程 ID 取得常見問題
- **POST /api/courses/{courseId}/faqs**: 建立新的課程常見問題
- **GET /api/course-faqs/{id}**: 依 ID 取得課程常見問題
- **PUT /api/course-faqs/{id}**: 更新課程常見問題
- **DELETE /api/course-faqs/{id}**: 刪除課程常見問題
- **GET /api/courses/{courseId}/faqs/next-faq-number**: 取得下一個可用的常見問題編號

### Course Groups (課程群組)

- **GET /api/course-groups**: 取得所有課程群組
- **GET /api/course-groups/{code}**: 依代碼取得指定課程群組

### Course Precautions (課程注意事項)

- **GET /api/course-precaution-groups**: 取得所有課程注意事項群組
- **GET /api/courses/{courseId}/precautions**: 取得指定課程的所有注意事項
- **GET /api/courses/{courseId}/precautions/{groupCode}**: 依群組代碼取得課程注意事項
- **PUT /api/courses/{courseId}/precautions/{groupCode}**: 更新指定群組的課程注意事項

### Course Steps (課程步驟)

- **GET /api/courses/{courseId}/steps**: 依課程 ID 取得步驟
- **POST /api/courses/{courseId}/steps**: 建立新的課程步驟
- **GET /api/course-steps/{id}**: 依 ID 取得課程步驟
- **PUT /api/course-steps/{id}**: 更新課程步驟
- **DELETE /api/course-steps/{id}**: 刪除課程步驟
- **GET /api/courses/{courseId}/steps/next-step-number**: 取得下一個可用的步驟編號

### Data References (資料參考)

- **GET /api/known-pipeline**: 取得"得知管道"的資料
- **GET /api/gender**: 取得性別選項資料

### Districts (區域)

- **GET /api/districts**: 取得區域列表

### User (使用者)

- **POST /api/cms-users/register**: 註冊新的 CMS 使用者
- **GET /api/cms-users**: 取得 CMS 使用者列表
- **GET /api/cms-users/{id}**: 依 ID 取得 CMS 使用者詳細資料
- **PUT /api/cms-users/{id}**: 更新 CMS 使用者
- **PATCH /api/cms-users/{id}/state**: 更新 CMS 使用者狀態
- **POST /api/employees/register**: 註冊新員工
- **GET /api/employees**: 取得員工列表
- **GET /api/employees/{id}**: 依 ID 取得員工詳細資料
- **PUT /api/employees/{id}**: 更新員工資料
- **DELETE /api/employees/{id}**: 刪除員工
- **PATCH /api/employees/{id}/state**: 更新員工狀態
- **POST /api/member/register**: 註冊新會員
- **POST /api/member/register/verify**: 驗證會員註冊 OTP
- **GET /api/member/profile**: 取得會員個人資料
- **PUT /api/member/profile**: 更新會員個人資料
- **PUT /api/member/email**: 更新會員 Email
- **PUT /api/member/address**: 更新會員地址
- **PUT /api/member/emergency-contact**: 更新會員緊急聯絡人
- **PUT /api/member/password**: 更新會員密碼
- **PUT /api/member/line-account**: 更新會員 LINE 帳號

### Member (會員)

- **GET /api/members**: 依電話或 Email 取得單一會員
- **POST /api/members**: 建立會員
- **GET /api/members/orders**: 查詢會員訂單
- **PUT /api/members/{id}**: 更新會員資料
- **GET /api/members/search**: 搜尋會員清單
- **GET /api/members/reservation/{id}**: 取得會員預約紀錄
- **GET /api/members/{memberId}/reservations**: 查詢會員的預約單
- **GET /api/members/{memberId}/courses/usage**: 查詢會員課程使用統計

### Group / Department (群組/部門)

- **GET /api/groups**: 取得群組/部門列表
- **POST /api/groups**: 建立新群組/部門
- **GET /api/groups/{code}**: 依代碼取得群組/部門
- **PUT /api/groups/{code}**: 更新群組/部門資訊
- **DELETE /api/groups/{code}**: 刪除群組/部門
- **PATCH /api/groups/{code}/state**: 更新群組/部門狀態

### Language (語言)

- **GET /api/languages**: 取得可用語言
- **POST /api/languages/switch**: 切換語言
- **GET /api/languages/current**: 取得目前語言
- **GET /api/languages/translations/{locale}**: 取得指定地區的翻譯

### Marketing Campaigns (行銷活動)

- **GET /api/marketing-campaigns**: 取得行銷活動列表
- **POST /api/marketing-campaigns**: 建立新的行銷活動
- **GET /api/marketing_campaigns/{id}**: 取得單筆行銷活動詳細資料
- **PUT /api/marketing_campaigns/{id}**: 更新行銷活動
- **PATCH /api/marketing_campaigns/{id}/status**: 變更行銷活動狀態

### Modules (模組)

- **GET /api/modules**: 取得所有依群組分類的模組

### Orders (訂單)

- **GET /api/orders**: 取得訂單列表
- **POST /api/orders**: 建立新訂單
- **POST /api/orders/quote**: 預先試算訂單
- **GET /api/orders/{id}**: 取得單一訂單詳情
- **PUT /orders/{id}**: 更新訂單
- **POST /api/orders/{orderId}/apply-stored-credit**: 使用儲值金支付訂單
- **PUT /api/orders/{orderId}/state**: 更新訂單狀態
- **POST /api/orders/{orderId}/reservations**: 由訂單開立預約
- **POST /api/orders/{orderId}/cancel**: 取消訂單

### Payments (付款)

- **POST /api/payments/checkout**: 建立結帳
- **POST /api/payments/oen/webhook**: OEN Webhook 回呼
- **GET /api/payments/{id}/payment-summary**: 查詢付款摘要

### Payment Methods (付款方式)

- **GET /api/payment-methods**: 取得付款方式列表
- **POST /api/payment-methods**: 建立新的付款方式
- **GET /api/payment-methods/{code}**: 取得付款方式詳細資料
- **PUT /api/payment-methods/{code}**: 更新付款方式
- **DELETE /api/payment-methods/{code}**: 刪除付款方式
- **PATCH /api/payment-methods/{code}/state**: 變更付款方式狀態

### Permissions (權限)

- **GET /api/permissions/{module_code}**: 依模組代碼取得權限

### PrepaidPurchase (儲值金購買)

- **POST /api/prepaid-purchases**: 建立儲值金購買紀錄
- **GET /api/prepaid-purchases/{id}**: 列出會員的儲值金購買紀錄
- **PUT /api/prepaid-purchases/{id}**: 更新儲值金購買紀錄

### Prepaid (儲值金)

- **GET /api/prepaid**: 列出儲值金設定
- **POST /api/prepaid**: 建立儲值金設定
- **GET /api/prepaid/{id}**: 取得儲值金設定詳細資料
- **PUT /api/prepaid/{id}**: 更新儲值金設定

### Products (商品)

- **GET /api/products**: 取得商品列表
- **POST /api/products**: 建立商品
- **GET /api/products/{id}**: 取得指定商品資料
- **PUT /api/products/{id}**: 更新商品
- **PATCH /api/products/{id}/state**: 變更商品狀態

### ProductsBrand (商品品牌)

- **GET /api/products_brands**: 取得商品品牌列表
- **POST /api/products_brands**: 建立商品品牌
- **PUT /api/products_brands/batch-update**: 批次更新商品品牌
- **DELETE /api/products_brands/{id}**: 刪除商品品牌

### ProductsCategory (商品品類)

- **GET /api/products_categories**: 取得商品品類列表
- **POST /api/products_categories**: 建立商品品類
- **PUT /api/products_categories/batch-update**: 批次更新商品品類
- **DELETE /api/products_categories/{id}**: 刪除商品品類

### ProductsEffect (商品功效)

- **GET /api/products_effects**: 取得商品功效列表
- **POST /api/products_effects**: 建立商品功效
- **PUT /api/products_effects/batch-update**: 批次更新商品功效
- **DELETE /api/products_effects/{id}**: 刪除商品功效

### ProductsLevel (商品分級)

- **GET /api/products_levels**: 取得商品分級列表
- **POST /api/products_levels**: 建立商品分級
- **PUT /api/products_levels/batch-update**: 批次更新商品分級
- **DELETE /api/products_levels/{id}**: 刪除商品分級

### ProductsSeries (商品系列)

- **GET /api/products_series**: 取得商品系列列表
- **POST /api/products_series**: 建立商品系列
- **PUT /api/products_series/batch-update**: 批次更新商品系列
- **DELETE /api/products_series/{id}**: 刪除商品系列

### ProductsServiceItem (商品服務項目)

- **GET /api/products_service_items**: 取得商品服務項目列表
- **POST /api/products_service_items**: 建立商品服務項目
- **PUT /api/products_service_items/batch-update**: 批次更新商品服務項目
- **DELETE /api/products_service_items/{id}**: 刪除商品服務項目

### ProductsSource (商品來源)

- **GET /api/products_sources**: 取得商品來源列表
- **POST /api/products_sources**: 建立商品來源
- **PUT /api/products_sources/batch-update**: 批次更新商品來源
- **DELETE /api/products_sources/{id}**: 刪除商品來源

### ProductsSupplier (商品供應商)

- **GET /api/products_suppliers**: 取得商品供應商列表
- **POST /api/products_suppliers**: 建立商品供應商
- **PUT /api/products_suppliers/batch-update**: 批次更新商品供應商
- **DELETE /api/products_suppliers/{id}**: 刪除商品供應商

### ProductsType (商品類別)

- **GET /api/products_types**: 取得商品類別列表
- **POST /api/products_types**: 建立商品類別
- **PUT /api/products_types/batch-update**: 批次更新商品類別
- **DELETE /api/products_types/{id}**: 刪除商品類別

### ProductsUnit (商品單位)

- **GET /api/products_units**: 取得商品單位列表
- **POST /api/products_units**: 建立商品單位
- **PUT /api/products_units/batch-update**: 批次更新商品單位
- **DELETE /api/products_units/{id}**: 刪除商品單位

### Programs (課程)

- **GET /api/programs**: 取得課程列表
- **POST /api/programs**: 新增課程
- **GET /api/programs/bookable**: 取得可預約課程列表
- **GET /api/programs/{id}**: 取得單一課程詳情
- **PUT /api/programs/{id}**: 更新課程
- **PATCH /api/programs/{id}/state**: 變更課程狀態

### ProgramStoreSpaces (課程門市空間)

- **GET /api/program_store_spaces**: 查詢課程門市空間設定
- **GET /api/program_store_spaces_public**: (公開)查詢課程門市空間設定
- **POST /api/program_store_spaces/update**: 更新課程門市空間設定

### ProgramsBrand (課程品牌)

- **GET /api/programs_brands**: 取得課程品牌列表
- **POST /api/programs_brands**: 建立課程品牌
- **PUT /api/programs_brands/batch-update**: 批次更新課程品牌
- **DELETE /api/programs_brands/{id}**: 刪除課程品牌

### ProgramsCourseType (課程性質)

- **GET /api/programs_course_types**: 取得課程性質列表
- **POST /api/programs_course_types**: 建立課程性質
- **PUT /api/programs_course_types/batch-update**: 批次更新課程性質
- **DELETE /api/programs_course_types/{id}**: 刪除課程性質

### ProgramsProductLine (課程系列)

- **GET /api/programs_product_lines**: 取得課程系列列表
- **POST /api/programs_product_lines**: 建立課程系列
- **PUT /api/programs_product_lines/batch-update**: 批次更新課程系列
- **DELETE /api/programs_product_lines/{id}**: 刪除課程系列

### ProgramsRegistrationRestrictions (預約限制)

- **GET /api/programs_reservation_limits**: 取得目前有效的預約限制設定
- **POST /api/programs_reservation_limits**: 更新預約限制設定

### ProgramsServiceItem (課程服務項目)

- **GET /api/programs_service_items**: 取得課程服務項目列表
- **POST /api/programs_service_items**: 建立課程服務項目
- **PUT /api/programs_service_items/batch-update**: 批次更新課程服務項目
- **DELETE /api/programs_service_items/{id}**: 刪除服務項目

### Reservations (預約)

- **GET /api/reservations**: 取得預約單列表
- **POST /api/reservations**: 建立預約單
- **GET /api/reservations/{id}**: 取得預約單詳細資料
- **PUT /api/reservations/{id}**: 更新預約單
- **PUT /api/reservations/{id}/status**: 更新預約單狀態

### RolePermissions (角色權限)

- **GET /api/roles**: 取得角色列表
- **POST /api/roles**: 建立新角色
- **GET /api/roles/{code}/permissions**: 依角色代碼取得權限
- **PUT /api/roles/{role_code}**: 更新角色及其權限
- **DELETE /api/roles/{role_code}**: 刪除角色
- **PATCH /api/roles/{role_code}/state**: 更新角色狀態
- **PATCH /api/role/{code}/name**: 更新角色名稱

### Service Location Facilities (服務地點設施)

- **GET /api/service-location-facilities**: 取得所有服務地點設施
- **PUT /api/service-location-facilities/batch-update**: 批次更新服務地點設施

### Service Location Services (服務地點服務項目)

- **GET /api/service-location-services**: 取得所有服務地點服務項目
- **PUT /api/service-location-services/batch-update**: 批次更新服務地點服務項目

### Service Location Stores (服務地點門市)

- **GET /api/service-location-stores**: 取得服務地點門市列表
- **POST /api/service-location-stores**: 建立新的服務地點門市
- **GET /api/service-location-stores/{id}**: 取得指定的服務地點門市
- **PUT /api/service-location-stores/{id}**: 更新服務地點門市
- **DELETE /api/service-location-stores/{id}**: 刪除服務地點門市
- **GET /api/service-location-stores/statuses**: 取得可用於篩選的狀態
- **GET /api/service-location-stores/locations**: 取得可用於篩選的地點

### SMS Logs (簡訊日誌)

- **GET /api/sms-logs**: 取得簡訊日誌
- **GET /api/sms-logs/{id}**: 依 ID 取得簡訊日誌
- **GET /api/sms-logs/statistics**: 取得簡訊統計
- **POST /api/sms-logs/{id}/retry**: 重試失敗的簡訊

### Inventory (庫存)

- **GET /api/stock**: 查詢庫存
- **GET /api/stock/product-ledger**: 以商品查詢庫存異動紀錄

### Inventory Safeties (安全庫存)

- **POST /api/stock/inventory_safety/{warehouse_id}/batchupdate**: 批次設定倉庫商品的安全庫存

### Inventory Audits (庫存盤點)

- **POST /api/stock/inventory_audit/{warehouse_id}/batchupdate**: 單一倉庫盤點批次上傳

### StockInOrders (進貨單)

- **GET /api/stock-in-orders**: 取得進貨單清單
- **POST /api/stock-in-orders**: 建立進貨單
- **GET /api/stock/in-orders/{order_id}**: 查看單筆進貨單

### StockOutBaseType (出貨基本類型)

- **GET /api/stockout_basetype**: 取得出貨基本類型列表
- **POST /api/stock_out_base_types**: 建立出貨基本類型
- **PUT /api/stock_out_base_types/batch-update**: 批次更新出貨基本類型
- **DELETE /api/stock_out_base_types/{id}**: 刪除出貨基本類型

### StockOutOrders (出貨單)

- **GET /api/stock/stock-out**: 出貨單列表
- **POST /api/stock/stock-out**: 建立出貨單
- **POST /api/stock/stock-out/{order_id}/cancel**: 作廢出貨單
- **PUT /api/stock/stock-out/{id}**: 更新出貨單
- **GET /api/stock/out-orders/{order_id}**: 查看單筆出貨單

### Stock Transfers (調撥單)

- **GET /api/stock/transfers**: 調撥單列表查詢
- **POST /api/stock/transfers/{transfer_id}/reject**: 拒絕調撥單
- **POST /api/stock/transfers/{transfer_id}/confirm-ship**: 確認並出貨
- **POST /api/stock/transfers/{transfer_id}/count-items**: 點貨
- **POST /api/stock/transfers/{transfer_id}/receive-close**: 點收入庫並結案
- **GET /api/stock/transfers/{transfer_id}**: 查看單筆調撥單
- **DELETE /api/stock/transfers/{transfer_id}**: 刪除調撥單

### Stores (門市)

- **GET /api/stores**: 取得門市列表
- **POST /api/stores**: 建立新門市
- **GET /api/stores/{code}**: 依代碼取得門市詳細資料
- **PUT /api/stores/{code}**: 更新門市
- **PATCH /api/stores/{code}/state**: 變更門市狀態

### StoreServiceItem (門市服務項目)

- **GET /api/store-service-items**: 取得所有門市對應的服務項目
- **POST /api/store-service-items/sync**: 更新門市與服務項目關聯
- **GET /api/store-service-items/{store_code}**: 查詢單一門市的服務項目

### Warehouses (倉庫)

- **POST /api/warehouses**: 建立倉庫
- **GET /api/warehouses/{id}**: 取得倉庫詳細資料
- **PUT /api/warehouses/{id}**: 更新倉庫資料
- **PUT /api/warehouses/{id}/state**: 更新倉庫狀態

### Web Content (網頁內容)

- **GET /api/web-content/{group_type}**: 依群組類型取得所有內容
- **GET /api/web-content/{group_type}/{code}**: 依群組類型及代碼取得內容
- **POST /api/web-content/{group_type}/{code}**: 建立或更新網頁內容
- **POST /api/web-content/upload-image**: 上傳圖片
- **POST /api/web-content/upload-video**: 上傳影片
- **DELETE /api/web-content/delete-video**: 刪除影片
- **DELETE /api/web-content/delete-image**: 刪除圖片
- **GET /api/web-content/list-images**: 取得 S3 資料夾中的圖片列表

### Work Schedule (班表)

- **GET /api/work-schedules/schedules**: 取得每日班表
- **GET /api/work-schedules/public-schedules**: 取得班表 (公開)
- **GET /api/work-schedules**: 取得班表
- **GET /api/work-schedules/export**: 匯出班表 (Excel)

### Zone (區域)

- **GET /api/zones**: 取得區域列表
- **POST /api/zones**: 建立新區域
- **GET /api/zones/{code}**: 依代碼取得區域詳細資料
- **PUT /api/zones/{code}**: 更新區域
- **PATCH /api/zones/{code}/state**: 變更區域狀態
