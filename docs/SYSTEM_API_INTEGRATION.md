# 系統 API 整合文檔

本文檔說明了新增的 13 個系統 API 端點的整合和使用方法。

## 概述

已成功整合以下 13 個 API 端點：

1. **地區資料** - `GET /api/districts`
2. **付款方式** - `GET /api/payment-methods`
3. **服務地點設施** - `GET /api/service-location-facilities`
4. **服務地點服務** - `GET /api/service-location-services`
5. **服務地點店鋪** - `GET /api/service-location-stores`
6. **性別資料** - `GET /api/gender`
7. **已知管道** - `GET /api/known-pipeline`
8. **課程群組** - `GET /api/course-groups`
9. **課程資料** - `GET /api/courses`
10. **城市資料** - `GET /api/cities`
11. **美容師服務部位** - `GET /api/beautician-service-parts`
12. **活動日誌** - `GET /api/activity-logs`
13. **活動日誌統計** - `GET /api/activity-logs/statistics`

## 文件結構

### API 模塊
- `src/api/system.ts` - 包含所有系統 API 的類型定義和請求函數

### 路由配置
- `src/router/modules/system.ts` - 系統管理模塊的路由配置

### 視圖組件
- `src/views/system/` - 包含所有系統管理頁面
  - `districts.vue` - 地區資料頁面
  - `payment-methods.vue` - 付款方式頁面
  - `service-location-facilities.vue` - 服務地點設施頁面
  - `service-location-services.vue` - 服務地點服務頁面
  - `service-location-stores.vue` - 服務地點店鋪頁面
  - `gender.vue` - 性別資料頁面
  - `known-pipeline.vue` - 已知管道頁面
  - `course-groups.vue` - 課程群組頁面
  - `courses.vue` - 課程資料頁面
  - `cities.vue` - 城市資料頁面
  - `beautician-service-parts.vue` - 美容師服務部位頁面
  - `activity-logs.vue` - 活動日誌頁面
  - `activity-logs-statistics.vue` - 活動日誌統計頁面
  - `api-test.vue` - API 測試頁面

### 通用組件
- `src/components/SystemDataTable/index.vue` - 可重用的數據表格組件

## 功能特點

### 1. 統一的數據表格組件
- 支持搜尋功能
- 響應式設計
- 加載狀態指示
- 錯誤處理
- 可選分頁功能
- 統計信息顯示

### 2. 自動 Token 管理
- 系統 API 端點自動使用提供的 JWT token
- 無需手動配置認證

### 3. 錯誤處理
- 友好的錯誤提示
- 網絡錯誤重試機制
- 詳細的錯誤日誌

### 4. 響應式設計
- 適配不同屏幕尺寸
- 移動設備友好

## 使用方法

### 1. 訪問系統管理頁面
在側邊欄中點擊「系統管理」即可看到所有可用的子頁面。

### 2. 搜尋功能
每個頁面都提供搜尋功能，可以根據關鍵字過濾數據。

### 3. 刷新數據
點擊「刷新」按鈕可以重新獲取最新數據。

### 4. API 測試
訪問「API 測試」頁面可以測試所有 API 端點的連通性和響應。

## 技術實現

### API 請求配置
```typescript
// HTTP 客戶端自動為系統 API 添加認證 token
const systemApiEndpoints = [
  "/api/districts",
  "/api/payment-methods",
  // ... 其他端點
];

if (isSystemApi) {
  config.headers["Authorization"] = `Bearer ${fixedToken}`;
}
```

### 代理配置
```typescript
// vite.config.ts
proxy: {
  "/api": {
    target: "https://sbar-posweb-api.pascation.com.tw",
    changeOrigin: true,
    secure: false
  }
}
```

### 類型安全
所有 API 響應都有完整的 TypeScript 類型定義，確保類型安全。

## 故障排除

### 1. API 調用失敗
- 檢查網絡連接
- 確認 token 是否有效
- 查看瀏覽器控制台錯誤信息

### 2. 數據顯示異常
- 檢查 API 響應格式是否符合預期
- 確認數據類型定義是否正確

### 3. 頁面加載錯誤
- 檢查路由配置
- 確認組件文件路徑正確

## 擴展指南

### 添加新的 API 端點
1. 在 `src/api/system.ts` 中添加類型定義和請求函數
2. 在 `src/utils/http/index.ts` 中添加端點到 `systemApiEndpoints` 數組
3. 創建對應的視圖組件
4. 在路由配置中添加新路由

### 自定義數據表格
使用 `SystemDataTable` 組件時，可以通過 props 和 slots 進行自定義：

```vue
<SystemDataTable
  title="自定義標題"
  :columns="columns"
  :api-function="apiFunction"
  :show-pagination="true"
>
  <template #custom-slot="{ row }">
    <!-- 自定義內容 -->
  </template>
</SystemDataTable>
```

## 注意事項

1. **Token 有效期**：當前使用的 JWT token 有有效期限制，過期後需要更新
2. **API 限制**：某些 API 可能有調用頻率限制
3. **數據格式**：不同 API 的響應格式可能略有差異，組件已做兼容處理
4. **權限控制**：確保用戶有訪問相應 API 的權限

## 更新日誌

- **2025-01-17**: 初始版本，完成 13 個 API 端點的整合
- 包含完整的視圖頁面、路由配置和錯誤處理
- 實現響應式設計和統一的用戶體驗
