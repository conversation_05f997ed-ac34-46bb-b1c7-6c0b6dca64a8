<template>
  <div class="p-4">
    <el-card shadow="never" :body-style="{ padding: '0' }">
      <template #header>
        <div class="flex items-center justify-between">
          <span>{{ title }}</span>
          <div class="flex gap-2">
            <el-input
              v-if="showSearch"
              v-model="searchKeyword"
              :placeholder="searchPlaceholder"
              clearable
              class="w-72"
              @keyup.enter="handleSearch"
            />
            <el-button 
              v-if="showSearch"
              type="primary" 
              :loading="loading" 
              @click="handleSearch"
            >
              搜尋
            </el-button>
            <el-button 
              type="success" 
              :loading="loading" 
              @click="handleRefresh"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="p-4">
        <!-- 錯誤提示 -->
        <el-alert
          v-if="error"
          :title="error"
          type="error"
          show-icon
          :closable="false"
          class="mb-4"
        />

        <!-- 數據表格 -->
        <el-table 
          v-else
          :data="tableData" 
          stripe 
          class="w-full" 
          :height="tableHeight"
          :empty-text="emptyText"
        >
          <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :show-overflow-tooltip="column.showOverflowTooltip !== false"
            :formatter="column.formatter"
          >
            <template v-if="column.slot" #default="scope">
              <slot :name="column.slot" :row="scope.row" :column="column" :index="scope.$index" />
            </template>
          </el-table-column>
        </el-table>

        <!-- 分頁 -->
        <div v-if="showPagination && pagination" class="flex justify-center mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <!-- 統計信息 -->
        <div v-if="showStats && !loading && !error" class="mt-4 text-sm text-gray-600">
          <span>共 {{ totalCount }} 條記錄</span>
          <span v-if="pagination">
            ，當前第 {{ pagination.current_page }} 頁，共 {{ pagination.last_page || Math.ceil(pagination.total / pagination.per_page) }} 頁
          </span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";

// Props 定義
interface Column {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  showOverflowTooltip?: boolean;
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
  slot?: string;
}

interface Pagination {
  current_page: number;
  per_page: number;
  total: number;
  last_page?: number;
  from?: number;
  to?: number;
}

interface Props {
  title: string;
  columns: Column[];
  apiFunction: (params?: any) => Promise<any>;
  showSearch?: boolean;
  searchPlaceholder?: string;
  showPagination?: boolean;
  showStats?: boolean;
  tableHeight?: string;
  emptyText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true,
  searchPlaceholder: "請輸入搜尋關鍵字",
  showPagination: false,
  showStats: true,
  tableHeight: "60vh",
  emptyText: "暫無數據"
});

// 響應式數據
const loading = ref(false);
const error = ref("");
const tableData = ref<any[]>([]);
const searchKeyword = ref("");
const currentPage = ref(1);
const pageSize = ref(20);
const pagination = ref<Pagination | null>(null);

// 計算屬性
const totalCount = computed(() => {
  return pagination.value?.total || tableData.value.length;
});

// 方法
const fetchData = async () => {
  try {
    loading.value = true;
    error.value = "";
    
    const params: any = {};
    
    // 添加搜尋參數
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim();
    }
    
    // 添加分頁參數
    if (props.showPagination) {
      params.page = currentPage.value;
      params.per_page = pageSize.value;
    }
    
    const response = await props.apiFunction(params);
    
    if (response?.data) {
      // 處理分頁響應
      if (response.data.data && Array.isArray(response.data.data)) {
        tableData.value = response.data.data;
        pagination.value = response.data.pagination || null;
      }
      // 處理直接數組響應
      else if (Array.isArray(response.data)) {
        tableData.value = response.data;
        pagination.value = null;
      }
      // 處理其他格式
      else {
        tableData.value = [];
        console.warn("Unexpected response format:", response);
      }
    } else {
      tableData.value = [];
    }
  } catch (err: any) {
    console.error("API Error:", err);
    error.value = err?.message || err?.response?.data?.message || "獲取數據失敗";
    tableData.value = [];
    ElMessage.error(error.value);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchData();
};

const handleRefresh = () => {
  searchKeyword.value = "";
  currentPage.value = 1;
  fetchData();
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1;
  fetchData();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  fetchData();
};

// 生命週期
onMounted(() => {
  fetchData();
});

// 暴露方法給父組件
defineExpose({
  refresh: fetchData,
  search: handleSearch
});
</script>

<style scoped>
.el-card {
  border-radius: 8px;
}

.el-table {
  border-radius: 4px;
}
</style>
