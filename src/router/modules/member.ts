const Layout = () => import("@/layout/index.vue");

export default {
  path: "/member",
  name: "Member",
  component: Layout,
  redirect: "/member/list",
  meta: {
    icon: "ep/user",
    title: "會員",
    rank: 11
  },
  children: [
    {
      path: "/member/list",
      name: "MemberList",
      component: () => import("@/views/member/list.vue"),
      meta: {
        title: "會員列表",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
