const Layout = () => import("@/layout/index.vue");

export default {
  path: "/system",
  name: "System",
  component: Layout,
  redirect: "/system/districts",
  meta: {
    icon: "ep/setting",
    title: "系統管理",
    rank: 20
  },
  children: [
    {
      path: "/system/districts",
      name: "SystemDistricts",
      component: () => import("@/views/system/districts.vue"),
      meta: {
        title: "地區資料",
        showLink: true
      }
    },
    {
      path: "/system/payment-methods",
      name: "SystemPaymentMethods",
      component: () => import("@/views/system/payment-methods.vue"),
      meta: {
        title: "付款方式",
        showLink: true
      }
    },
    {
      path: "/system/service-location-facilities",
      name: "SystemServiceLocationFacilities",
      component: () => import("@/views/system/service-location-facilities.vue"),
      meta: {
        title: "服務地點設施",
        showLink: true
      }
    },
    {
      path: "/system/service-location-services",
      name: "SystemServiceLocationServices",
      component: () => import("@/views/system/service-location-services.vue"),
      meta: {
        title: "服務地點服務",
        showLink: true
      }
    },
    {
      path: "/system/service-location-stores",
      name: "SystemServiceLocationStores",
      component: () => import("@/views/system/service-location-stores.vue"),
      meta: {
        title: "服務地點店鋪",
        showLink: true
      }
    },
    {
      path: "/system/gender",
      name: "SystemGender",
      component: () => import("@/views/system/gender.vue"),
      meta: {
        title: "性別資料",
        showLink: true
      }
    },
    {
      path: "/system/known-pipeline",
      name: "SystemKnownPipeline",
      component: () => import("@/views/system/known-pipeline.vue"),
      meta: {
        title: "已知管道",
        showLink: true
      }
    },
    {
      path: "/system/course-groups",
      name: "SystemCourseGroups",
      component: () => import("@/views/system/course-groups.vue"),
      meta: {
        title: "課程群組",
        showLink: true
      }
    },
    {
      path: "/system/courses",
      name: "SystemCourses",
      component: () => import("@/views/system/courses.vue"),
      meta: {
        title: "課程資料",
        showLink: true
      }
    },
    {
      path: "/system/cities",
      name: "SystemCities",
      component: () => import("@/views/system/cities.vue"),
      meta: {
        title: "城市資料",
        showLink: true
      }
    },
    {
      path: "/system/beautician-service-parts",
      name: "SystemBeauticianServiceParts",
      component: () => import("@/views/system/beautician-service-parts.vue"),
      meta: {
        title: "美容師服務部位",
        showLink: true
      }
    },
    {
      path: "/system/activity-logs",
      name: "SystemActivityLogs",
      component: () => import("@/views/system/activity-logs.vue"),
      meta: {
        title: "活動日誌",
        showLink: true
      }
    },
    {
      path: "/system/activity-logs-statistics",
      name: "SystemActivityLogsStatistics",
      component: () => import("@/views/system/activity-logs-statistics.vue"),
      meta: {
        title: "活動日誌統計",
        showLink: true
      }
    },
    {
      path: "/system/api-test",
      name: "SystemApiTest",
      component: () => import("@/views/system/api-test.vue"),
      meta: {
        title: "API 測試",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
