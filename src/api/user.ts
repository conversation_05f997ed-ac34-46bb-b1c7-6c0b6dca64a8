import { http } from "@/utils/http";

export type UserResult = {
  success: boolean;
  data: {
    /** 头像 */
    avatar: string;
    /** 用户名 */
    username: string;
    /** 昵称 */
    nickname: string;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** 按钮级别权限 */
    permissions: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http
    .request<any>("post", "/api/login", {
      data
    })
    .then((res: any): UserResult => {
      const d: any = res?.data ?? {};
      const hasSnakeCase =
        Object.prototype.hasOwnProperty.call(d, "access_token") ||
        Object.prototype.hasOwnProperty.call(d, "expires_in") ||
        Object.prototype.hasOwnProperty.call(d, "refresh_token");

      if (hasSnakeCase) {
        const accessToken: string = d.access_token ?? "";
        const refreshToken: string = d.refresh_token ?? "";
        const expiresInSec: number = Number(d.expires_in ?? 0);
        const expires: string = new Date(
          Date.now() + expiresInSec * 1000
        ).toISOString();

        return {
          success: Boolean(res?.success),
          data: {
            accessToken,
            refreshToken,
            expires: new Date(expires),
            avatar: d.avatar ?? "",
            username: d.username ?? "",
            nickname: d.nickname ?? "",
            roles: d.roles ?? [],
            permissions: d.permissions ?? []
          }
        } as UserResult;
      }

      return res as UserResult;
    });
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/api/refresh-token", {
    data
  });
};
