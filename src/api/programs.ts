import { http } from "@/utils/http";

export type Program = {
  id: string;
  name: string;
  code: string;
  service_item_name: string | null;
  total_course_duration: number | null;
  retail_price: string | number | null;
  membership_price: string | number | null;
  employee_price: string | number | null;
  status: string | number | null;
  course_description: string | null;
  image: string | null;
  created_at: string | null;
  updated_at: string | null;
  updated_by_name: string | null;
  stores_spaces?: Array<{
    store_code: string;
    store_name: string;
    store_zone_code: string;
    spaces: string[];
  }>;
  [key: string]: any;
};

export type ProgramsResponse = {
  success: boolean;
  data: Program[];
  meta?: {
    current_page?: number;
    last_page?: number;
    per_page?: number;
    total?: number;
    [key: string]: any;
  };
};

/**
 * 取得課程清單
 * - 對應 GET /api/programs
 * - 可傳入查詢參數：{ page, per_page, keyword, ... }（依後端為準）
 */
export const getPrograms = (params?: Record<string, any>) => {
  return http.request<ProgramsResponse>("get", "/api/programs", { params });
};
