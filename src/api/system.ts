import { http } from "@/utils/http";

// 地區資料類型
export type District = {
  id: string;
  name: string;
  code?: string;
  parent_id?: string;
  level?: number;
  [key: string]: any;
};

// 付款方式類型
export type PaymentMethod = {
  id: string;
  name: string;
  code: string;
  status?: string | number;
  description?: string;
  [key: string]: any;
};

// 服務地點設施類型
export type ServiceLocationFacility = {
  id: string;
  name: string;
  code?: string;
  description?: string;
  status?: string | number;
  [key: string]: any;
};

// 服務地點服務類型
export type ServiceLocationService = {
  id: string;
  name: string;
  code?: string;
  description?: string;
  status?: string | number;
  [key: string]: any;
};

// 服務地點店鋪類型
export type ServiceLocationStore = {
  id: string;
  name: string;
  code: string;
  address?: string;
  phone?: string;
  status?: string | number;
  district_id?: string;
  city_id?: string;
  [key: string]: any;
};

// 性別資料類型
export type Gender = {
  id: string;
  name: string;
  code: string;
  [key: string]: any;
};

// 已知管道類型
export type KnownPipeline = {
  id: string;
  name: string;
  code?: string;
  description?: string;
  status?: string | number;
  [key: string]: any;
};

// 課程群組類型
export type CourseGroup = {
  id: string;
  name: string;
  code?: string;
  description?: string;
  status?: string | number;
  [key: string]: any;
};

// 課程資料類型
export type Course = {
  id: string;
  name: string;
  code: string;
  description?: string;
  duration?: number;
  price?: string | number;
  status?: string | number;
  course_group_id?: string;
  [key: string]: any;
};

// 城市資料類型
export type City = {
  id: string;
  name: string;
  code?: string;
  country_id?: string;
  [key: string]: any;
};

// 美容師服務部位類型
export type BeauticianServicePart = {
  id: string;
  name: string;
  code?: string;
  description?: string;
  status?: string | number;
  [key: string]: any;
};

// 活動日誌類型
export type ActivityLog = {
  id: string;
  user_id?: string;
  action: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  [key: string]: any;
};

// 活動日誌統計類型
export type ActivityLogStatistics = {
  total_logs: number;
  today_logs: number;
  this_week_logs: number;
  this_month_logs: number;
  top_actions?: Array<{
    action: string;
    count: number;
  }>;
  [key: string]: any;
};

// 通用響應類型
export type ApiResponse<T> = {
  httpCode?: number;
  httpMessage?: string;
  success?: boolean;
  data: T;
  message?: string;
};

// 分頁響應類型
export type PaginatedResponse<T> = ApiResponse<{
  data: T[];
  pagination?: {
    current_page: number;
    per_page: number;
    total: number;
    last_page?: number;
    from?: number;
    to?: number;
  };
}>;

/**
 * 獲取地區資料
 * GET /api/districts
 */
export const getDistricts = (params?: Record<string, any>) => {
  return http.request<ApiResponse<District[]>>("get", "/api/districts", {
    params
  });
};

/**
 * 獲取付款方式
 * GET /api/payment-methods
 */
export const getPaymentMethods = (params?: Record<string, any>) => {
  return http.request<ApiResponse<PaymentMethod[]>>("get", "/api/payment-methods", {
    params
  });
};

/**
 * 獲取服務地點設施
 * GET /api/service-location-facilities
 */
export const getServiceLocationFacilities = (params?: Record<string, any>) => {
  return http.request<ApiResponse<ServiceLocationFacility[]>>("get", "/api/service-location-facilities", {
    params
  });
};

/**
 * 獲取服務地點服務
 * GET /api/service-location-services
 */
export const getServiceLocationServices = (params?: Record<string, any>) => {
  return http.request<ApiResponse<ServiceLocationService[]>>("get", "/api/service-location-services", {
    params
  });
};

/**
 * 獲取服務地點店鋪
 * GET /api/service-location-stores
 */
export const getServiceLocationStores = (params?: Record<string, any>) => {
  return http.request<ApiResponse<ServiceLocationStore[]>>("get", "/api/service-location-stores", {
    params
  });
};

/**
 * 獲取性別資料
 * GET /api/gender
 */
export const getGender = (params?: Record<string, any>) => {
  return http.request<ApiResponse<Gender[]>>("get", "/api/gender", {
    params
  });
};

/**
 * 獲取已知管道
 * GET /api/known-pipeline
 */
export const getKnownPipeline = (params?: Record<string, any>) => {
  return http.request<ApiResponse<KnownPipeline[]>>("get", "/api/known-pipeline", {
    params
  });
};

/**
 * 獲取課程群組
 * GET /api/course-groups
 */
export const getCourseGroups = (params?: Record<string, any>) => {
  return http.request<ApiResponse<CourseGroup[]>>("get", "/api/course-groups", {
    params
  });
};

/**
 * 獲取課程資料
 * GET /api/courses
 */
export const getCourses = (params?: Record<string, any>) => {
  return http.request<ApiResponse<Course[]>>("get", "/api/courses", {
    params
  });
};

/**
 * 獲取城市資料
 * GET /api/cities
 */
export const getCities = (params?: Record<string, any>) => {
  return http.request<ApiResponse<City[]>>("get", "/api/cities", {
    params
  });
};

/**
 * 獲取美容師服務部位
 * GET /api/beautician-service-parts
 */
export const getBeauticianServiceParts = (params?: Record<string, any>) => {
  return http.request<ApiResponse<BeauticianServicePart[]>>("get", "/api/beautician-service-parts", {
    params
  });
};

/**
 * 獲取活動日誌
 * GET /api/activity-logs
 */
export const getActivityLogs = (params?: Record<string, any>) => {
  return http.request<PaginatedResponse<ActivityLog>>("get", "/api/activity-logs", {
    params
  });
};

/**
 * 獲取活動日誌統計
 * GET /api/activity-logs/statistics
 */
export const getActivityLogStatistics = (params?: Record<string, any>) => {
  return http.request<ApiResponse<ActivityLogStatistics>>("get", "/api/activity-logs/statistics", {
    params
  });
};
