import { http } from "@/utils/http";

export type Member = {
  id: string;
  mem_code: string;
  name: string;
  mem_en_name: string | null;
  gender: string | null;
  email: string | null;
  phone: string | null;
  birth_date: string | null;
  mem_level: string | null;
  join_date: string | null;
  mem_expiry: string | null;
  status: string | number | null;
  mem_status: string | null;
  total_spent: string | number | null;
  blacklist: string | null;
  email_verification: number | null;
  no_show_times: number | null;
  create_by: string | null;
  updated_by: string | null;
  created_at: string | null;
  updated_at: string | null;
  // 允許後端擴充
  [key: string]: any;
};

export type MembersSearchResponse = {
  httpCode?: number;
  httpMessage?: string;
  data: {
    members: Member[];
    pagination?: {
      current_page: number;
      per_page: number;
      total: number;
      last_page?: number;
      from?: number;
      to?: number;
    };
  };
};

/**
 * 搜尋會員清單
 * - 對應 GET /api/members/search
 * - 支援參數：page, per_page, keyword 等（依後端為準）
 */
export const searchMembers = (params?: Record<string, any>) => {
  return http.request<MembersSearchResponse>("get", "/api/members/search", {
    params
  });
};
