import { http } from "@/utils/http";

export type Product = Record<string, any>;

export type ProductListResponse = {
  success: boolean;
  /** 商品清單（結構依後端為準） */
  data: Product[];
  /** 可選的分頁資訊（若後端提供） */
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
  };
};

/**
 * 取得商品列表
 * - 將參數透過 querystring 帶入，如 { page, pageSize, keyword, ... }
 * - 基礎路徑使用 VITE_API_BASE_URL，開發環境會經由 Vite 代理轉發（vite.config.ts -> server.proxy）
 */
export const getProducts = (params?: Record<string, any>) => {
  return http.request<ProductListResponse>("get", "/api/products", {
    params
  });
};
