import { http } from "@/utils/http";

export type Zone = {
  code: string;
  name: string;
  description: string | null;
  status: string; // e.g. "disable" | "enable"
  updated_by: string | null;
  created_at: string | null;
  updated_at: string | null;
  deleted_at: string | null;
  updated_by_name: string | null;
  [key: string]: any;
};

export type ZonesResponse = {
  success: boolean;
  message: string;
  data: Zone[];
};

/**
 * 取得 Zone 清單
 * - 對應 GET /api/zones
 * - 可傳入查詢參數：{ page, per_page, keyword, ... }（依後端為準）
 */
export const getZones = (params?: Record<string, any>) => {
  return http.request<ZonesResponse>("get", "/api/zones", { params });
};
