<template>
  <div class="p-4">
    <el-card shadow="never" :body-style="{ padding: '0' }">
      <template #header>
        <div class="flex items-center justify-between">
          <span>會員列表</span>
          <div class="flex gap-2">
            <el-input
              v-model="keyword"
              placeholder="搜尋姓名/電話/Email/會員代碼"
              clearable
              class="w-72"
              @keyup.enter="onSearch"
            />
            <el-button type="primary" :loading="loading" @click="onSearch">搜尋</el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="p-4">
        <el-table :data="list" stripe class="w-full" height="60vh">
          <el-table-column prop="id" label="ID" min-width="220" show-overflow-tooltip />
          <el-table-column prop="mem_code" label="會員代碼" min-width="160" show-overflow-tooltip />
          <el-table-column prop="name" label="姓名" min-width="140" show-overflow-tooltip />
          <el-table-column prop="mem_en_name" label="英文名" min-width="140" show-overflow-tooltip />
          <el-table-column prop="gender" label="性別" width="100" />
          <el-table-column prop="email" label="Email" min-width="200" show-overflow-tooltip />
          <el-table-column prop="phone" label="電話" min-width="160" show-overflow-tooltip />
          <el-table-column label="生日" width="140">
            <template #default="{ row }">{{ formatDate(row.birth_date, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column prop="mem_level" label="等級" width="120" />
          <el-table-column label="加入日期" width="160">
            <template #default="{ row }">{{ formatDate(row.join_date, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column label="會籍到期" width="160">
            <template #default="{ row }">{{ formatDate(row.mem_expiry, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column label="狀態" width="120">
            <template #default="{ row }">
              <el-tag :type="statusType(row.status)">{{ statusText(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="mem_status" label="會員狀態" width="140" />
          <el-table-column label="累計消費" width="140">
            <template #default="{ row }">{{ formatCurrency(row.total_spent) }}</template>
          </el-table-column>
          <el-table-column label="黑名單" width="110">
            <template #default="{ row }">
              <el-tag :type="row.blacklist === 'Y' ? 'danger' : 'info'">{{ row.blacklist || 'N' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="email_verification" label="Email驗證" width="120" />
          <el-table-column prop="no_show_times" label="爽約次數" width="120" />
          <el-table-column prop="create_by" label="建立人" min-width="200" show-overflow-tooltip />
          <el-table-column prop="updated_by" label="更新人" min-width="200" show-overflow-tooltip />
          <el-table-column label="建立時間" min-width="180">
            <template #default="{ row }">{{ formatDate(row.created_at) }}</template>
          </el-table-column>
          <el-table-column label="更新時間" min-width="180">
            <template #default="{ row }">{{ formatDate(row.updated_at) }}</template>
          </el-table-column>
        </el-table>

        <div class="p-4 flex justify-end">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page-size="pageSize"
            :current-page="page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { searchMembers } from "@/api/members";

const loading = ref(false);
const keyword = ref("");
const page = ref(1);
const pageSize = ref(20);
const total = ref(0);
const list = ref<Record<string, any>[]>([]);

async function fetchList() {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: page.value,
      per_page: pageSize.value
    };
    const kw = keyword.value.trim();
    if (kw) params.keyword = kw;

    const res: any = await searchMembers(params);
    const rows = res?.data?.members;
    list.value = Array.isArray(rows) ? rows : [];
    const pg = res?.data?.pagination;
    if (pg) {
      total.value = Number(pg.total) || list.value.length;
      page.value = Number(pg.current_page) || page.value;
      pageSize.value = Number(pg.per_page) || pageSize.value;
    } else {
      total.value = list.value.length;
    }
  } catch (e: any) {
    ElMessage.error(e?.message || "獲取會員列表失敗");
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  page.value = 1;
  fetchList();
}

function handleSizeChange(size: number) {
  pageSize.value = size;
  page.value = 1;
  fetchList();
}

function handleCurrentChange(p: number) {
  page.value = p;
  fetchList();
}

onMounted(fetchList);

// 金額格式化（TWD）
const currencyFormatter = new Intl.NumberFormat("zh-TW", {
  style: "currency",
  currency: "TWD",
  minimumFractionDigits: 0
});
function formatCurrency(v: string | number | null | undefined) {
  const n = Number(v);
  if (Number.isFinite(n)) return currencyFormatter.format(n);
  return v == null ? "" : String(v);
}

function statusType(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "success" : "info";
}
function statusText(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "啟用" : "停用";
}

function formatDate(v: string | null | undefined, fmt = "YYYY-MM-DD HH:mm") {
  if (!v) return "";
  return dayjs(v).isValid() ? dayjs(v).format(fmt) : v;
}
</script>

<style scoped>
</style>
