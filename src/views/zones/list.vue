<template>
  <div class="p-4">
    <el-card shadow="never" :body-style="{ padding: '0' }">
      <template #header>
        <div class="flex items-center justify-between">
          <span>區域列表</span>
          <div class="flex gap-2">
            <el-input
              v-model="keyword"
              placeholder="搜尋名稱或代碼"
              clearable
              class="w-60"
              @keyup.enter="onSearch"
            />
            <el-button type="primary" :loading="loading" @click="onSearch">
              搜尋
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="p-4">
        <el-table :data="list" stripe class="w-full" height="60vh">
          <el-table-column prop="code" label="代碼" width="140" />
          <el-table-column prop="name" label="名稱" min-width="220" />
          <el-table-column
            prop="description"
            label="描述"
            min-width="260"
            show-overflow-tooltip
          />
          <el-table-column prop="status" label="狀態" width="120">
            <template #default="{ row }">
              <el-tag :type="statusType(row.status)">
                {{ statusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_by_name" label="更新人" width="140" />
          <el-table-column label="更新時間" min-width="180">
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="p-4 flex justify-end">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page-size="pageSize"
            :current-page="page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getZones } from "@/api/zones";
import dayjs from "dayjs";

const loading = ref(false);
const keyword = ref("");
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);
const list = ref<Record<string, any>[]>([]);

async function fetchList() {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: page.value,
      per_page: pageSize.value
    };
    const kw = keyword.value.trim();
    if (kw) params.keyword = kw;

    const res: any = await getZones(params);
    // 根據實際樣本：{ success, message, data: Zone[] }
    list.value = Array.isArray(res?.data) ? res.data : [];
    total.value = list.value.length;
  } catch (e: any) {
    ElMessage.error(e?.message || "獲取 Zones 失敗");
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  page.value = 1;
  fetchList();
}

function handleSizeChange(size: number) {
  pageSize.value = size;
  page.value = 1;
  fetchList();
}

function handleCurrentChange(p: number) {
  page.value = p;
  fetchList();
}

onMounted(fetchList);

function statusType(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "success" : "info";
}
function statusText(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "啟用" : "停用";
}

function formatDate(v: string | null | undefined) {
  if (!v) return "";
  return dayjs(v).isValid() ? dayjs(v).format("YYYY-MM-DD HH:mm") : v;
}
</script>

<style scoped>
</style>
