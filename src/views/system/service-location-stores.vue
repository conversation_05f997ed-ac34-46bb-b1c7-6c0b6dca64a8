<template>
  <SystemDataTable
    title="服務地點店鋪"
    :columns="columns"
    :api-function="getServiceLocationStores"
    search-placeholder="搜尋店鋪名稱、代碼或地址"
  >
    <template #status="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
  </SystemDataTable>
</template>

<script setup lang="ts">
import { getServiceLocationStores } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "code",
    label: "店鋪代碼",
    width: 120
  },
  {
    prop: "name",
    label: "店鋪名稱",
    minWidth: 150
  },
  {
    prop: "address",
    label: "地址",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: "phone",
    label: "電話",
    width: 150
  },
  {
    prop: "city_id",
    label: "城市ID",
    width: 120,
    showOverflowTooltip: true
  },
  {
    prop: "district_id",
    label: "地區ID",
    width: 120,
    showOverflowTooltip: true
  },
  {
    prop: "status",
    label: "狀態",
    width: 100,
    slot: "status"
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  },
  {
    prop: "updated_at",
    label: "更新時間",
    width: 180,
    formatter: (row: any) => {
      return row.updated_at ? dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];

// 狀態相關方法
const getStatusType = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "success";
  if (status === 0 || status === "inactive" || status === "停用") return "danger";
  return "info";
};

const getStatusText = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "啟用";
  if (status === 0 || status === "inactive" || status === "停用") return "停用";
  return status || "未知";
};
</script>

<style scoped>
/* 組件特定樣式 */
</style>
