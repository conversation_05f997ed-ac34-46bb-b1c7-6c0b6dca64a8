<template>
  <SystemDataTable
    title="課程資料"
    :columns="columns"
    :api-function="getCourses"
    search-placeholder="搜尋課程名稱或代碼"
  >
    <template #status="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
    
    <template #price="{ row }">
      <span class="font-medium text-green-600">
        {{ formatCurrency(row.price) }}
      </span>
    </template>
    
    <template #duration="{ row }">
      <el-tag type="info" size="small">
        {{ formatDuration(row.duration) }}
      </el-tag>
    </template>
  </SystemDataTable>
</template>

<script setup lang="ts">
import { getCourses } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "code",
    label: "課程代碼",
    width: 120
  },
  {
    prop: "name",
    label: "課程名稱",
    minWidth: 150
  },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: "duration",
    label: "時長",
    width: 100,
    slot: "duration"
  },
  {
    prop: "price",
    label: "價格",
    width: 120,
    slot: "price"
  },
  {
    prop: "course_group_id",
    label: "課程群組ID",
    width: 150,
    showOverflowTooltip: true
  },
  {
    prop: "status",
    label: "狀態",
    width: 100,
    slot: "status"
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  },
  {
    prop: "updated_at",
    label: "更新時間",
    width: 180,
    formatter: (row: any) => {
      return row.updated_at ? dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];

// 狀態相關方法
const getStatusType = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "success";
  if (status === 0 || status === "inactive" || status === "停用") return "danger";
  return "info";
};

const getStatusText = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "啟用";
  if (status === 0 || status === "inactive" || status === "停用") return "停用";
  return status || "未知";
};

// 格式化貨幣
const formatCurrency = (value: any) => {
  if (!value && value !== 0) return "-";
  const num = parseFloat(value.toString());
  return isNaN(num) ? "-" : `$${num.toLocaleString()}`;
};

// 格式化時長
const formatDuration = (value: any) => {
  if (!value && value !== 0) return "-";
  const num = parseInt(value.toString());
  if (isNaN(num)) return "-";
  
  if (num >= 60) {
    const hours = Math.floor(num / 60);
    const minutes = num % 60;
    return minutes > 0 ? `${hours}小時${minutes}分鐘` : `${hours}小時`;
  }
  return `${num}分鐘`;
};
</script>

<style scoped>
/* 組件特定樣式 */
</style>
