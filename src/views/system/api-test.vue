<template>
  <div class="p-4">
    <el-card shadow="never">
      <template #header>
        <span>API 測試頁面</span>
      </template>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <el-button 
            v-for="api in apiList" 
            :key="api.name"
            :loading="api.loading"
            :type="api.success ? 'success' : api.error ? 'danger' : 'primary'"
            @click="testApi(api)"
          >
            {{ api.name }}
            <span v-if="api.success" class="ml-2">✓</span>
            <span v-if="api.error" class="ml-2">✗</span>
          </el-button>
        </div>
        
        <el-divider />
        
        <div class="space-y-4">
          <h3>測試結果：</h3>
          <div v-for="result in testResults" :key="result.name" class="border rounded p-4">
            <div class="flex items-center justify-between mb-2">
              <h4 class="font-medium">{{ result.name }}</h4>
              <el-tag :type="result.success ? 'success' : 'danger'">
                {{ result.success ? '成功' : '失敗' }}
              </el-tag>
            </div>
            <div v-if="result.error" class="text-red-600 text-sm mb-2">
              錯誤: {{ result.error }}
            </div>
            <div v-if="result.data" class="text-sm">
              <details>
                <summary class="cursor-pointer text-blue-600">查看數據 ({{ result.count }} 條記錄)</summary>
                <pre class="mt-2 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">{{ JSON.stringify(result.data, null, 2) }}</pre>
              </details>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import {
  getDistricts,
  getPaymentMethods,
  getServiceLocationFacilities,
  getServiceLocationServices,
  getServiceLocationStores,
  getGender,
  getKnownPipeline,
  getCourseGroups,
  getCourses,
  getCities,
  getBeauticianServiceParts,
  getActivityLogs,
  getActivityLogStatistics
} from "@/api/system";

// API 列表
const apiList = reactive([
  { name: "地區資料", func: getDistricts, loading: false, success: false, error: false },
  { name: "付款方式", func: getPaymentMethods, loading: false, success: false, error: false },
  { name: "服務地點設施", func: getServiceLocationFacilities, loading: false, success: false, error: false },
  { name: "服務地點服務", func: getServiceLocationServices, loading: false, success: false, error: false },
  { name: "服務地點店鋪", func: getServiceLocationStores, loading: false, success: false, error: false },
  { name: "性別資料", func: getGender, loading: false, success: false, error: false },
  { name: "已知管道", func: getKnownPipeline, loading: false, success: false, error: false },
  { name: "課程群組", func: getCourseGroups, loading: false, success: false, error: false },
  { name: "課程資料", func: getCourses, loading: false, success: false, error: false },
  { name: "城市資料", func: getCities, loading: false, success: false, error: false },
  { name: "美容師服務部位", func: getBeauticianServiceParts, loading: false, success: false, error: false },
  { name: "活動日誌", func: getActivityLogs, loading: false, success: false, error: false },
  { name: "活動日誌統計", func: getActivityLogStatistics, loading: false, success: false, error: false }
]);

const testResults = ref<any[]>([]);

const testApi = async (api: any) => {
  api.loading = true;
  api.success = false;
  api.error = false;
  
  try {
    const response = await api.func();
    console.log(`${api.name} 響應:`, response);
    
    api.success = true;
    
    // 處理響應數據
    let data = response?.data;
    let count = 0;
    
    if (Array.isArray(data)) {
      count = data.length;
    } else if (data?.data && Array.isArray(data.data)) {
      count = data.data.length;
      data = data.data;
    } else if (typeof data === 'object') {
      count = 1;
    }
    
    // 更新測試結果
    const existingIndex = testResults.value.findIndex(r => r.name === api.name);
    const result = {
      name: api.name,
      success: true,
      data: data,
      count: count,
      error: null
    };
    
    if (existingIndex >= 0) {
      testResults.value[existingIndex] = result;
    } else {
      testResults.value.push(result);
    }
    
    ElMessage.success(`${api.name} 測試成功`);
  } catch (error: any) {
    console.error(`${api.name} 錯誤:`, error);
    
    api.error = true;
    
    const errorMessage = error?.message || error?.response?.data?.message || "未知錯誤";
    
    // 更新測試結果
    const existingIndex = testResults.value.findIndex(r => r.name === api.name);
    const result = {
      name: api.name,
      success: false,
      data: null,
      count: 0,
      error: errorMessage
    };
    
    if (existingIndex >= 0) {
      testResults.value[existingIndex] = result;
    } else {
      testResults.value.push(result);
    }
    
    ElMessage.error(`${api.name} 測試失敗: ${errorMessage}`);
  } finally {
    api.loading = false;
  }
};
</script>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.gap-4 {
  gap: 1rem;
}
</style>
