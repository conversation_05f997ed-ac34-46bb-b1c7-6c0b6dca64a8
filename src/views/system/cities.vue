<template>
  <SystemDataTable
    title="城市資料"
    :columns="columns"
    :api-function="getCities"
    search-placeholder="搜尋城市名稱或代碼"
  />
</template>

<script setup lang="ts">
import { getCities } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "code",
    label: "城市代碼",
    width: 120
  },
  {
    prop: "name",
    label: "城市名稱",
    minWidth: 150
  },
  {
    prop: "country_id",
    label: "國家ID",
    width: 150,
    showOverflowTooltip: true
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  },
  {
    prop: "updated_at",
    label: "更新時間",
    width: 180,
    formatter: (row: any) => {
      return row.updated_at ? dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];
</script>

<style scoped>
/* 組件特定樣式 */
</style>
