<template>
  <SystemDataTable
    title="性別資料"
    :columns="columns"
    :api-function="getGender"
    search-placeholder="搜尋性別名稱或代碼"
  >
    <template #gender_display="{ row }">
      <el-tag :type="getGenderType(row.code)" size="small">
        {{ row.name }}
      </el-tag>
    </template>
  </SystemDataTable>
</template>

<script setup lang="ts">
import { getGender } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "code",
    label: "性別代碼",
    width: 120
  },
  {
    prop: "name",
    label: "性別名稱",
    width: 120,
    slot: "gender_display"
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  },
  {
    prop: "updated_at",
    label: "更新時間",
    width: 180,
    formatter: (row: any) => {
      return row.updated_at ? dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];

// 性別類型方法
const getGenderType = (code: string) => {
  switch (code?.toLowerCase()) {
    case "m":
    case "male":
    case "男":
      return "primary";
    case "f":
    case "female":
    case "女":
      return "danger";
    default:
      return "info";
  }
};
</script>

<style scoped>
/* 組件特定樣式 */
</style>
