<template>
  <div class="p-4">
    <el-card shadow="never" :body-style="{ padding: '0' }">
      <template #header>
        <div class="flex items-center justify-between">
          <span>活動日誌統計</span>
          <el-button 
            type="success" 
            :loading="loading" 
            @click="fetchData"
          >
            刷新
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="p-6">
        <!-- 錯誤提示 -->
        <el-alert
          v-if="error"
          :title="error"
          type="error"
          show-icon
          :closable="false"
          class="mb-6"
        />

        <!-- 統計卡片 -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <el-card class="stat-card">
            <div class="flex items-center">
              <div class="stat-icon bg-blue-100 text-blue-600">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">
                  {{ formatNumber(statistics.total_logs) }}
                </div>
                <div class="text-sm text-gray-500">總日誌數</div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="flex items-center">
              <div class="stat-icon bg-green-100 text-green-600">
                <el-icon size="24"><Calendar /></el-icon>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">
                  {{ formatNumber(statistics.today_logs) }}
                </div>
                <div class="text-sm text-gray-500">今日日誌</div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="flex items-center">
              <div class="stat-icon bg-yellow-100 text-yellow-600">
                <el-icon size="24"><Clock /></el-icon>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">
                  {{ formatNumber(statistics.this_week_logs) }}
                </div>
                <div class="text-sm text-gray-500">本週日誌</div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="flex items-center">
              <div class="stat-icon bg-purple-100 text-purple-600">
                <el-icon size="24"><TrendCharts /></el-icon>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">
                  {{ formatNumber(statistics.this_month_logs) }}
                </div>
                <div class="text-sm text-gray-500">本月日誌</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 熱門動作統計 -->
        <el-card v-if="statistics.top_actions && statistics.top_actions.length > 0" class="mt-6">
          <template #header>
            <span class="text-lg font-medium">熱門動作統計</span>
          </template>
          
          <div class="space-y-4">
            <div 
              v-for="(action, index) in statistics.top_actions" 
              :key="action.action"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                  {{ index + 1 }}
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">{{ action.action }}</div>
                </div>
              </div>
              <div class="flex items-center">
                <div class="text-sm text-gray-500 mr-4">{{ formatNumber(action.count) }} 次</div>
                <div class="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-blue-600 h-2 rounded-full" 
                    :style="{ width: getPercentage(action.count) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 其他統計信息 -->
        <div v-if="!loading && !error && Object.keys(statistics).length > 4" class="mt-6">
          <el-card>
            <template #header>
              <span class="text-lg font-medium">詳細統計</span>
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                v-for="(value, key) in otherStats" 
                :key="key"
                class="flex justify-between items-center p-3 bg-gray-50 rounded"
              >
                <span class="text-sm text-gray-600">{{ formatKey(key) }}</span>
                <span class="text-sm font-medium">{{ formatValue(value) }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getActivityLogStatistics } from "@/api/system";
import { Document, Calendar, Clock, TrendCharts } from "@element-plus/icons-vue";

// 響應式數據
const loading = ref(false);
const error = ref("");
const statistics = ref<any>({
  total_logs: 0,
  today_logs: 0,
  this_week_logs: 0,
  this_month_logs: 0,
  top_actions: []
});

// 計算屬性
const otherStats = computed(() => {
  const excludeKeys = ['total_logs', 'today_logs', 'this_week_logs', 'this_month_logs', 'top_actions'];
  const result: any = {};
  
  Object.keys(statistics.value).forEach(key => {
    if (!excludeKeys.includes(key)) {
      result[key] = statistics.value[key];
    }
  });
  
  return result;
});

// 方法
const fetchData = async () => {
  try {
    loading.value = true;
    error.value = "";
    
    const response = await getActivityLogStatistics();
    
    if (response?.data) {
      statistics.value = response.data;
    } else {
      statistics.value = {
        total_logs: 0,
        today_logs: 0,
        this_week_logs: 0,
        this_month_logs: 0,
        top_actions: []
      };
    }
  } catch (err: any) {
    console.error("API Error:", err);
    error.value = err?.message || err?.response?.data?.message || "獲取統計數據失敗";
    ElMessage.error(error.value);
  } finally {
    loading.value = false;
  }
};

const formatNumber = (num: any) => {
  if (!num && num !== 0) return "0";
  return parseInt(num.toString()).toLocaleString();
};

const getPercentage = (count: number) => {
  if (!statistics.value.top_actions || statistics.value.top_actions.length === 0) return 0;
  const maxCount = Math.max(...statistics.value.top_actions.map((a: any) => a.count));
  return maxCount > 0 ? (count / maxCount) * 100 : 0;
};

const formatKey = (key: string) => {
  return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatValue = (value: any) => {
  if (typeof value === 'number') {
    return formatNumber(value);
  }
  return value?.toString() || "-";
};

// 生命週期
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.stat-card {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-card {
  border-radius: 8px;
}
</style>
