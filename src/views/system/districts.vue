<template>
  <SystemDataTable
    title="地區資料"
    :columns="columns"
    :api-function="getDistricts"
    search-placeholder="搜尋地區名稱或代碼"
  >
    <template #status="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
    
    <template #level="{ row }">
      <el-tag :type="getLevelType(row.level)" size="small">
        {{ getLevelText(row.level) }}
      </el-tag>
    </template>
  </SystemDataTable>
</template>

<script setup lang="ts">
import { getDistricts } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "code",
    label: "地區代碼",
    width: 120
  },
  {
    prop: "name",
    label: "地區名稱",
    minWidth: 150
  },
  {
    prop: "parent_id",
    label: "上級地區ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "level",
    label: "層級",
    width: 100,
    slot: "level"
  },
  {
    prop: "status",
    label: "狀態",
    width: 100,
    slot: "status"
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  },
  {
    prop: "updated_at",
    label: "更新時間",
    width: 180,
    formatter: (row: any) => {
      return row.updated_at ? dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];

// 狀態相關方法
const getStatusType = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "success";
  if (status === 0 || status === "inactive" || status === "停用") return "danger";
  return "info";
};

const getStatusText = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "啟用";
  if (status === 0 || status === "inactive" || status === "停用") return "停用";
  return status || "未知";
};

// 層級相關方法
const getLevelType = (level: any) => {
  switch (level) {
    case 1: return "primary";
    case 2: return "success";
    case 3: return "warning";
    default: return "info";
  }
};

const getLevelText = (level: any) => {
  switch (level) {
    case 1: return "一級";
    case 2: return "二級";
    case 3: return "三級";
    case 4: return "四級";
    default: return level || "未知";
  }
};
</script>

<style scoped>
/* 組件特定樣式 */
</style>
