<template>
  <SystemDataTable
    title="活動日誌"
    :columns="columns"
    :api-function="getActivityLogs"
    :show-pagination="true"
    search-placeholder="搜尋用戶ID、動作或描述"
  >
    <template #action="{ row }">
      <el-tag :type="getActionType(row.action)" size="small">
        {{ row.action }}
      </el-tag>
    </template>
    
    <template #user_agent="{ row }">
      <el-tooltip :content="row.user_agent" placement="top">
        <span class="truncate max-w-32 inline-block">
          {{ getBrowserInfo(row.user_agent) }}
        </span>
      </el-tooltip>
    </template>
  </SystemDataTable>
</template>

<script setup lang="ts">
import { getActivityLogs } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "user_id",
    label: "用戶ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "action",
    label: "動作",
    width: 120,
    slot: "action"
  },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: "ip_address",
    label: "IP地址",
    width: 140
  },
  {
    prop: "user_agent",
    label: "瀏覽器",
    width: 150,
    slot: "user_agent"
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];

// 動作類型方法
const getActionType = (action: string) => {
  const actionLower = action?.toLowerCase() || "";
  
  if (actionLower.includes("create") || actionLower.includes("add")) return "success";
  if (actionLower.includes("update") || actionLower.includes("edit")) return "warning";
  if (actionLower.includes("delete") || actionLower.includes("remove")) return "danger";
  if (actionLower.includes("login") || actionLower.includes("auth")) return "primary";
  if (actionLower.includes("view") || actionLower.includes("read")) return "info";
  
  return "info";
};

// 獲取瀏覽器信息
const getBrowserInfo = (userAgent: string) => {
  if (!userAgent) return "-";
  
  // 簡單的瀏覽器檢測
  if (userAgent.includes("Chrome")) return "Chrome";
  if (userAgent.includes("Firefox")) return "Firefox";
  if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) return "Safari";
  if (userAgent.includes("Edge")) return "Edge";
  if (userAgent.includes("Opera")) return "Opera";
  
  return "其他";
};
</script>

<style scoped>
/* 組件特定樣式 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
