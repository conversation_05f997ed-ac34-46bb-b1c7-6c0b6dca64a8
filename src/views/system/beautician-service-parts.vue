<template>
  <SystemDataTable
    title="美容師服務部位"
    :columns="columns"
    :api-function="getBeauticianServiceParts"
    search-placeholder="搜尋服務部位名稱或代碼"
  >
    <template #status="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
  </SystemDataTable>
</template>

<script setup lang="ts">
import { getBeauticianServiceParts } from "@/api/system";
import SystemDataTable from "@/components/SystemDataTable/index.vue";
import dayjs from "dayjs";

// 表格列配置
const columns = [
  {
    prop: "id",
    label: "ID",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "code",
    label: "部位代碼",
    width: 120
  },
  {
    prop: "name",
    label: "部位名稱",
    minWidth: 150
  },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: "status",
    label: "狀態",
    width: 100,
    slot: "status"
  },
  {
    prop: "created_at",
    label: "創建時間",
    width: 180,
    formatter: (row: any) => {
      return row.created_at ? dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  },
  {
    prop: "updated_at",
    label: "更新時間",
    width: 180,
    formatter: (row: any) => {
      return row.updated_at ? dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss") : "-";
    }
  }
];

// 狀態相關方法
const getStatusType = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "success";
  if (status === 0 || status === "inactive" || status === "停用") return "danger";
  return "info";
};

const getStatusText = (status: any) => {
  if (status === 1 || status === "active" || status === "啟用") return "啟用";
  if (status === 0 || status === "inactive" || status === "停用") return "停用";
  return status || "未知";
};
</script>

<style scoped>
/* 組件特定樣式 */
</style>
