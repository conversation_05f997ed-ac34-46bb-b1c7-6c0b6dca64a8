<template>
  <div class="p-4">
    <el-card shadow="never" :body-style="{ padding: '0' }">
      <template #header>
        <div class="flex items-center justify-between">
          <span>課程列表</span>
          <div class="flex gap-2">
            <el-input
              v-model="keyword"
              placeholder="搜尋課程名稱/代碼"
              clearable
              class="w-60"
              @keyup.enter="onSearch"
            />
            <el-button type="primary" :loading="loading" @click="onSearch">
              搜尋
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="p-4">
        <el-table :data="list" stripe class="w-full" height="60vh">
          <el-table-column label="圖片" width="110">
            <template #default="{ row }">
              <el-image
                v-if="row.image"
                :src="row.image"
                fit="cover"
                style="width: 64px; height: 64px; border-radius: 6px;"
                preview-teleported
                :preview-src-list="[row.image]"
              />
              <div v-else class="text-gray-400">—</div>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="ID" min-width="220" show-overflow-tooltip />
          <el-table-column prop="code" label="代碼" width="140" />
          <el-table-column prop="name" label="名稱" min-width="200" />
          <el-table-column prop="service_item_name" label="服務項目" min-width="160" />
          <el-table-column prop="total_course_duration" label="時長(分)" width="120" />

          <el-table-column label="售價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.retail_price) }}</template>
          </el-table-column>
          <el-table-column label="會員價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.membership_price) }}</template>
          </el-table-column>
          <el-table-column label="員購價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.employee_price) }}</template>
          </el-table-column>

          <el-table-column label="狀態" width="120">
            <template #default="{ row }">
              <el-tag :type="statusType(row.status)">{{ statusText(row.status) }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="updated_by_name" label="更新人" width="140" />
          <el-table-column label="建立時間" min-width="180">
            <template #default="{ row }">{{ formatDate(row.created_at) }}</template>
          </el-table-column>
          <el-table-column label="更新時間" min-width="180">
            <template #default="{ row }">{{ formatDate(row.updated_at) }}</template>
          </el-table-column>
          <el-table-column prop="course_description" label="課程描述" min-width="240" show-overflow-tooltip />
          <el-table-column label="門市與空間" min-width="280">
            <template #default="{ row }">
              <div v-if="Array.isArray(row.stores_spaces) && row.stores_spaces.length">
                <div
                  v-for="(s, idx) in row.stores_spaces"
                  :key="`${row.id}-${idx}`"
                  class="mb-1"
                >
                  <div class="text-sm text-gray-600">
                    <strong>{{ s.store_name || s.store_code }}</strong>
                    <span v-if="s.store_zone_code" class="ml-1 text-gray-400">({{ s.store_zone_code }})</span>
                  </div>
                  <div class="flex flex-wrap gap-1 mt-1">
                    <el-tag
                      v-for="(sp, i2) in (s.spaces || [])"
                      :key="`${row.id}-${idx}-${i2}`"
                      type="info"
                      size="small"
                    >
                      {{ sp }}
                    </el-tag>
                  </div>
                </div>
              </div>
              <span v-else class="text-gray-400">—</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="p-4 flex justify-end">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page-size="pageSize"
            :current-page="page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getPrograms } from "@/api/programs";
import dayjs from "dayjs";

const loading = ref(false);
const keyword = ref("");
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);
const list = ref<Record<string, any>[]>([]);

async function fetchList() {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: page.value,
      per_page: pageSize.value
    };
    const kw = keyword.value.trim();
    if (kw) params.keyword = kw;

    const res: any = await getPrograms(params);
    list.value = Array.isArray(res?.data) ? res.data : [];
    // 兼容 meta 分頁
    const meta = res?.meta;
    if (meta) {
      total.value = Number(meta.total) || list.value.length;
      page.value = Number(meta.current_page) || page.value;
      pageSize.value = Number(meta.per_page) || pageSize.value;
    } else {
      total.value = list.value.length;
    }
  } catch (e: any) {
    ElMessage.error(e?.message || "獲取課程列表失敗");
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  page.value = 1;
  fetchList();
}

function handleSizeChange(size: number) {
  pageSize.value = size;
  page.value = 1;
  fetchList();
}

function handleCurrentChange(p: number) {
  page.value = p;
  fetchList();
}

onMounted(fetchList);

// 金額格式化（TWD）
const currencyFormatter = new Intl.NumberFormat("zh-TW", {
  style: "currency",
  currency: "TWD",
  minimumFractionDigits: 0
});

function formatCurrency(v: string | number | null | undefined) {
  const n = Number(v);
  if (Number.isFinite(n)) return currencyFormatter.format(n);
  return v == null ? "" : String(v);
}

// 狀態樣式與文字
function statusType(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "success" : "info";
}
function statusText(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "啟用" : "停用";
}

function formatDate(v: string | null | undefined) {
  if (!v) return "";
  return dayjs(v).isValid() ? dayjs(v).format("YYYY-MM-DD HH:mm") : v;
}
</script>

<style scoped>
</style>
