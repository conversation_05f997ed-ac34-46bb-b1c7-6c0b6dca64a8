<template>
  <div class="p-4">
    <el-card shadow="never" :body-style="{ padding: '0' }">
      <template #header>
        <div class="flex items-center justify-between">
          <span>產品列表</span>
          <div class="flex gap-2">
            <el-input
              v-model="keyword"
              placeholder="搜尋產品名稱"
              clearable
              class="w-60"
              @keyup.enter="onSearch"
            />
            <el-button
              type="primary"
              :loading="loading"
              @click="onSearch"
            >
              搜尋
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="p-4">
        <el-table :data="list" stripe class="w-full" height="60vh">
          <el-table-column label="圖片" width="110">
            <template #default="{ row }">
              <el-image
                v-if="row.image"
                :src="row.image"
                fit="cover"
                style="width: 64px; height: 64px; border-radius: 6px;"
                preview-teleported
                :preview-src-list="[row.image]"
              />
              <div v-else class="text-gray-400">—</div>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="ID" min-width="220" show-overflow-tooltip />
          <el-table-column prop="code" label="代碼" width="120" />
          <el-table-column prop="name" label="名稱" min-width="200" />
          <el-table-column prop="type_name" label="類型" width="140" />
          <el-table-column prop="category_name" label="分類" min-width="160" />
          <el-table-column prop="line_name" label="產品線" min-width="160" />
          <el-table-column prop="supplier_name" label="供應商" min-width="160" />

          <el-table-column label="售價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.retail_price) }}</template>
          </el-table-column>
          <el-table-column label="會員價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.membership_price) }}</template>
          </el-table-column>
          <el-table-column label="批發價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.wholesale_price) }}</template>
          </el-table-column>
          <el-table-column label="員購價" width="140">
            <template #default="{ row }">{{ formatCurrency(row.employee_price) }}</template>
          </el-table-column>

          <el-table-column prop="unit_name" label="單位" width="100" />

          <el-table-column label="狀態" width="120">
            <template #default="{ row }">
              <el-tag :type="statusType(row.status)">{{ statusText(row.status) }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="updated_by_name" label="更新人" width="140" />
          <el-table-column label="建立時間" min-width="180">
            <template #default="{ row }">{{ formatDate(row.created_at) }}</template>
          </el-table-column>
          <el-table-column label="更新時間" min-width="180">
            <template #default="{ row }">{{ formatDate(row.updated_at) }}</template>
          </el-table-column>
        </el-table>

        <div class="p-4 flex justify-end">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page-size="pageSize"
            :current-page="page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getProducts } from "@/api/products";
import dayjs from "dayjs";

const loading = ref(false);
const keyword = ref("");
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);
const list = ref<Record<string, any>[]>([]);

async function fetchList() {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: page.value,
      pageSize: pageSize.value
    };
    const kw = keyword.value.trim();
    if (kw) params.keyword = kw;

    const res: any = await getProducts(params);
    list.value = Array.isArray(res?.data) ? res.data : [];
    // 兼容兩種分頁格式：res.pagination 與 res.meta
    const pg = res?.pagination;
    const meta = res?.meta;
    if (pg) {
      total.value = Number(pg.total) || list.value.length;
      page.value = Number(pg.page) || page.value;
      pageSize.value = Number(pg.pageSize) || pageSize.value;
    } else if (meta) {
      total.value = Number(meta.total) || list.value.length;
      page.value = Number(meta.current_page) || page.value;
      pageSize.value = Number(meta.per_page) || pageSize.value;
    } else {
      total.value = list.value.length;
    }
  } catch (e: any) {
    ElMessage.error(e?.message || "獲取產品列表失敗");
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  page.value = 1;
  fetchList();
}

function handleSizeChange(size: number) {
  pageSize.value = size;
  page.value = 1;
  fetchList();
}

function handleCurrentChange(p: number) {
  page.value = p;
  fetchList();
}

onMounted(fetchList);

// 金額格式化（TWD）
const currencyFormatter = new Intl.NumberFormat("zh-TW", {
  style: "currency",
  currency: "TWD",
  minimumFractionDigits: 0
});

function formatCurrency(v: string | number | null | undefined) {
  const n = Number(v);
  if (Number.isFinite(n)) return currencyFormatter.format(n);
  return v == null ? "" : String(v);
}

// 狀態樣式與文字
function statusType(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "success" : "info";
}
function statusText(v: string | number | null | undefined) {
  const s = String(v ?? "").toLowerCase();
  const enabled = s === "1" || s === "enable" || s === "enabled" || s === "active";
  return enabled ? "啟用" : "停用";
}

function formatDate(v: string | null | undefined) {
  if (!v) return "";
  return dayjs(v).isValid() ? dayjs(v).format("YYYY-MM-DD HH:mm") : v;
}
</script>

<style scoped>
</style>
